package com.manaknight.app.ui.fragments.home

import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import android.widget.Toast
import Manaknight.R
import Manaknight.databinding.FragmentCreateCustomerBinding
import Manaknight.databinding.FragmentLineItemsBinding
import Manaknight.databinding.FragmentSignUpBinding
import android.app.Dialog
import android.text.Editable
import android.text.TextWatcher
import android.view.MotionEvent
import android.widget.EditText
import com.manaknight.app.extensions.checkIsEmpty
import com.manaknight.app.extensions.disableSpaces
import com.manaknight.app.extensions.hide
import com.manaknight.app.extensions.hideSoftKeyboard
import com.manaknight.app.extensions.setOnClickWithDebounce
import com.manaknight.app.extensions.show
import com.manaknight.app.extensions.snackBar
import com.manaknight.app.extensions.textToString
import com.manaknight.app.extensions.viewBinding
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.navigation.fragment.navArgs
import com.manaknight.app.data.local.AppPreferences
import com.manaknight.app.extensions.isEmailValid
import com.manaknight.app.extensions.showProgressBar
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.model.remote.profitPro.ClientDetailRespModel
import com.manaknight.app.model.remote.profitPro.CommonResponse
import com.manaknight.app.model.remote.profitPro.CompanyRequest
import com.manaknight.app.model.remote.profitPro.CustomerRespListModel
import com.manaknight.app.model.remote.profitPro.DrawsRespModel
import com.manaknight.app.model.remote.profitPro.JobDetailsRespModel
import com.manaknight.app.model.remote.profitPro.LinearRespListModel
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel2
import com.manaknight.app.model.remote.profitPro.TotalRespModel
import com.manaknight.app.network.Resource
import com.manaknight.app.ui.CreateEstimationFragment
import com.manaknight.app.ui.CreateEstimationFragmentDirections
import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
import com.manaknight.app.utils.DynamicLineItemManager
import org.koin.android.ext.android.inject
import androidx.appcompat.app.AlertDialog


class LineItemsFragment : Fragment(R.layout.fragment_line_items) {

    private val binding by viewBinding(FragmentLineItemsBinding::bind)
    private val args by navArgs<LineItemsFragmentArgs>()
    private val baasViewModel: BaasViewModel by viewModel()

    private lateinit var dialog:                                                                                                      Dialog
    private val pref by inject<AppPreferences>()

    private val allLineItem: ArrayList<JobDetailsRespModel> = ArrayList()
    private var totals: TotalRespModel? = null
    private var clientDetails: ClientDetailRespModel? = null
    private lateinit var dynamicLineItemManager: DynamicLineItemManager


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog = progressDialog(requireContext())
        
        // Initialize dynamic line item manager
        dynamicLineItemManager = DynamicLineItemManager(
            context = requireContext(),
            container = binding.dynamicLineItemsContainer,
            navController = findNavController(),
            projectID = args.projectID,
            onDeleteItem = { id -> delteItem(id) }
        )
        
        binding.headerInclude.backButton.setOnClickListener {
            // Check if there are no line items and show confirmation dialog
            if (allLineItem.isEmpty()) {
                showBackNavigationDialog()
            } else {
                findNavController().popBackStack()
            }
        }

        binding.headerInclude.addPlaterTitle.text =  args.customerName + " New Estimation"

        binding.btnAddLineItem.setOnClickListener {

            val action = LineItemsFragmentDirections.actionLineItemViewToAddlineItemView(
                args.projectID,
                1
            )
            if (action != null) {
                findNavController().navigate(action)
            }
        }
    }

    private fun getAllLineItem() {

        baasViewModel.getSingleProjectDetails(args.projectID)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()

                        allLineItem.clear()
                        it.data?.job_details?.let { it1 -> allLineItem.addAll(it1) }
                        it.data?.totals?.let {  it1 -> totals = it1 }
                        it.data?.client_details?.let {  it1 -> clientDetails = it1 }

                        updateAllData()
                        updateTotal()
                    }
                }
            }
    }

    private fun initializeDraws() {

        baasViewModel.initializeDraws(args.projectID)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        //snackBar("Server Error")
                        if (it.message == "Draws already initialized") {
                            moveToDrawScreen()
                        } else {
                            snackBar("Server Error")
                        }
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        moveToDrawScreen()
                    }
                }
            }
    }

    private fun delteItem(id: Int) {

        baasViewModel.deleteLineItems(id)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        //snackBar("Server Error")
                        if (it.message == "Draws already initialized") {
                            moveToDrawScreen()
                        } else {
                            snackBar("Server Error")
                        }
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        snackBar("Item deleted successfully.")
                        getAllLineItem()
                    }
                }
            }
    }


    fun moveToDrawScreen() {

        val action = LineItemsFragmentDirections.actionLineItemViewToDrawsDetailView(
            args.projectID,
            args.customerName
        )
        if (action != null) {
            findNavController().navigate(action)
        }
    }

    fun updateAllData() {
        if (allLineItem.isEmpty()) {
            binding.scrollableContent.visibility = View.GONE
            binding.noCustomer.visibility = View.VISIBLE
            binding.btnAddLineItem.visibility = View.VISIBLE
        } else {
            binding.scrollableContent.visibility = View.VISIBLE
            binding.noCustomer.visibility = View.GONE
            
            // Use dynamic manager to update line items with proper numbering
            dynamicLineItemManager.updateLineItems(allLineItem)
            
            // Show/hide add button based on number of line items
            binding.btnAddLineItem.visibility = if (allLineItem.size >= 3) View.GONE else View.VISIBLE
        }
    }

    fun updateTotal() {

        binding.totalLineItem.root.visibility = View.GONE

        if (allLineItem.size > 0) {
            totals?.let { total ->
                binding.totalLineItem.root.visibility = View.VISIBLE
                binding.totalLineItem.txtSalePrice1.text = "$${total.sale_price?.toInt() ?: 0}"
                binding.totalLineItem.txtProfitOverhead1.text =
                    "$${total.total_profit_overhead?.toInt() ?: 0}"
                binding.totalLineItem.txtLaboutBudget1.text = "$${total.labour_budget?.toInt() ?: 0}"
                binding.totalLineItem.txtMaterialBudget1.text =
                    "$${total.material_budget?.toInt() ?: 0}"

                binding.totalLineItem.btnSave.setOnClickListener {
                    dialog.show()
                    // Retrieve necessary data for updateProject
                    val changeCount = 0 // Set to 0 or retrieve if available
                    val customerId = clientDetails?.customer_id ?: 0
                    val userId = clientDetails?.user_id ?: 0
                    val profitOverhead = totals?.total_profit_overhead?.toString() ?: "0"
                    val hourlyRate = totals?.labour_budget?.toString() ?: "0"
                    val projectId = args.projectID

                    baasViewModel.updateProject(
                        changeCount = changeCount,
                        customerId = customerId,
                        userId = userId,
                        status = 3, // draft
                        profitOverhead = profitOverhead,
                        hourlyRate = hourlyRate,
                        id = projectId
                    ).observe(viewLifecycleOwner) {
                        when (it.status) {
                            Status.LOADING -> dialog.show()
                            Status.SUCCESS -> {
                                dialog.dismiss()
                                snackBar("Status updated to draft.")
                                findNavController().popBackStack(R.id.home, false)
                            }
                            Status.ERROR -> {
                                dialog.dismiss()
                                snackBar(it.message ?: "Failed to update status.")
                            }
                        }
                    }
                }
                binding.totalLineItem.btnContinue.setOnClickListener {
                    initializeDraws()
                }
            }
        } else {
            // Show message when no line items are added
            binding.totalLineItem.root.visibility = View.VISIBLE
//            binding.totalLineItem.txtSalePrice1.text = "No line items added"
//            binding.totalLineItem.txtProfitOverhead1.text = "Add line items to continue"


//            binding.totalLineItem.txtLaboutBudget1.text = ""
//            binding.totalLineItem.txtMaterialBudget1.text = ""

            
            // Hide the Save and Continue buttons when no line items
            binding.totalLineItem.btnSave.visibility = View.GONE
            binding.totalLineItem.btnContinue.visibility = View.GONE
            
            // Show a Cancel Project button instead
//            showCancelProjectButton()
        }
    }

    private fun showCancelProjectButton() {
        // Create a simple button for canceling the project
        val cancelButton = android.widget.Button(requireContext()).apply {
            text = "Cancel Project"
            setBackgroundColor(resources.getColor(R.color.red_500, null))
            setTextColor(resources.getColor(android.R.color.white, null))
            setOnClickListener {
                showCancelProjectDialog()
            }
        }
        
        // Add the button to the layout if it doesn't exist
        if (binding.totalLineItem.root.findViewById<android.widget.Button>(R.id.btnCancelProject) == null) {
            cancelButton.id = R.id.btnCancelProject
            binding.totalLineItem.root.addView(cancelButton)
        }
    }

    private fun showCancelProjectDialog() {
        val alertDialog = AlertDialog.Builder(requireContext())
            .setTitle("Cancel Project")
            .setMessage("Are you sure you want to cancel this project? This action cannot be undone.")
            .setPositiveButton("Cancel Project") { _, _ ->
                deleteProject()
            }
            .setNegativeButton("Keep Project") { dialog, _ ->
                dialog.dismiss()
            }
            .create()
        alertDialog.show()
    }

    private fun deleteProject() {
        dialog.show()
        baasViewModel.deleteProject(args.projectID).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.LOADING -> dialog.show()
                Status.SUCCESS -> {
                    dialog.dismiss()
                    snackBar("Project cancelled successfully.")
                    findNavController().popBackStack(R.id.home, false)
                }
                Status.ERROR -> {
                    dialog.dismiss()
                    snackBar(it.message ?: "Failed to cancel project.")
                }
            }
        }
    }

  override fun onResume() {
            super.onResume()
            (activity as AppCompatActivity?)?.supportActionBar?.hide()
      binding.headerInclude.backButton.show()
      getAllLineItem()
        }

        override fun onStop() {
            super.onStop()
            (activity as AppCompatActivity?)?.supportActionBar?.show()
        }

    private fun showBackNavigationDialog() {
        val alertDialog = AlertDialog.Builder(requireContext())
            .setTitle("No Line Items Added")
            .setMessage("You haven't added any line items to this project. Do you want to cancel the project or continue adding line items?")
            .setPositiveButton("Cancel Project") { _, _ ->
                deleteProject()
            }
            .setNegativeButton("Continue") { dialog, _ ->
                dialog.dismiss()
            }
            .setNeutralButton("Go Back") { _, _ ->
                findNavController().popBackStack()
            }
            .create()
        alertDialog.show()
    }

}
    
