<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="bottom_add_lineal" modulePackage="Manaknight" filePath="app\src\main\res\layout\bottom_add_lineal.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/bottom_add_lineal_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="411" endOffset="14"/></Target><Target id="@+id/header" view="LinearLayout"><Expressions/><location startLine="12" startOffset="4" endLine="77" endOffset="18"/></Target><Target id="@+id/addHeading" view="TextView"><Expressions/><location startLine="34" startOffset="16" endLine="46" endOffset="46"/></Target><Target id="@+id/backButton" view="ImageView"><Expressions/><location startLine="49" startOffset="12" endLine="54" endOffset="47"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="60" startOffset="12" endLine="74" endOffset="37"/></Target><Target id="@+id/mainLayout" view="ScrollView"><Expressions/><location startLine="86" startOffset="4" endLine="408" endOffset="16"/></Target><Target id="@+id/line1" view="LinearLayout"><Expressions/><location startLine="94" startOffset="4" endLine="407" endOffset="18"/></Target><Target id="@+id/edTxtLinealName" view="EditText"><Expressions/><location startLine="115" startOffset="4" endLine="131" endOffset="9"/></Target><Target id="@+id/tvFullName" view="TextView"><Expressions/><location startLine="133" startOffset="4" endLine="145" endOffset="66"/></Target><Target id="@+id/linearDefaultHourlyRate2" view="LinearLayout"><Expressions/><location startLine="147" startOffset="4" endLine="187" endOffset="18"/></Target><Target id="@+id/edTxtLinealCost" view="EditText"><Expressions/><location startLine="173" startOffset="8" endLine="186" endOffset="13"/></Target><Target id="@+id/tvFullNameError" view="TextView"><Expressions/><location startLine="189" startOffset="8" endLine="200" endOffset="38"/></Target><Target id="@+id/edTxtProfitOverHead" view="EditText"><Expressions/><location startLine="240" startOffset="8" endLine="254" endOffset="13"/></Target><Target id="@+id/remaingCost" view="TextView"><Expressions/><location startLine="257" startOffset="4" endLine="266" endOffset="37"/></Target><Target id="@+id/edTxtLabourCost" view="EditText"><Expressions/><location startLine="306" startOffset="8" endLine="319" endOffset="13"/></Target><Target id="@+id/edTxtMaterialCost" view="EditText"><Expressions/><location startLine="360" startOffset="8" endLine="374" endOffset="13"/></Target><Target id="@+id/tvMaterialCostError" view="TextView"><Expressions/><location startLine="376" startOffset="8" endLine="387" endOffset="38"/></Target><Target id="@+id/checkBox1" view="CheckBox"><Expressions/><location startLine="389" startOffset="8" endLine="406" endOffset="13"/></Target></Targets></Layout>