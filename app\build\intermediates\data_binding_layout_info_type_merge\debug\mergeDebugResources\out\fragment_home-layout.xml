<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="660" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="6" startOffset="4" endLine="658" endOffset="18"/></Target><Target id="@+id/headerInclude" tag="binding_1" include="header"><Expressions/><location startLine="13" startOffset="8" endLine="15" endOffset="37"/></Target><Target id="@+id/txtTotalProfitOverhead" view="TextView"><Expressions/><location startLine="78" startOffset="36" endLine="92" endOffset="75"/></Target><Target id="@+id/txtCompanyHealth" view="TextView"><Expressions/><location startLine="138" startOffset="36" endLine="152" endOffset="75"/></Target><Target id="@+id/btnViewDetails" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="165" startOffset="16" endLine="180" endOffset="49"/></Target><Target id="@+id/txtTotalContracts" view="TextView"><Expressions/><location startLine="209" startOffset="28" endLine="223" endOffset="67"/></Target><Target id="@+id/txtTotalAR" view="TextView"><Expressions/><location startLine="245" startOffset="28" endLine="260" endOffset="67"/></Target><Target id="@+id/txtMaterialBalance" view="TextView"><Expressions/><location startLine="292" startOffset="28" endLine="307" endOffset="67"/></Target><Target id="@+id/txtMaterialSpent" view="TextView"><Expressions/><location startLine="329" startOffset="28" endLine="344" endOffset="67"/></Target><Target id="@+id/txtLaborBalance" view="TextView"><Expressions/><location startLine="375" startOffset="28" endLine="390" endOffset="67"/></Target><Target id="@+id/txtLaborSpent" view="TextView"><Expressions/><location startLine="412" startOffset="28" endLine="427" endOffset="67"/></Target><Target id="@+id/firstRow" view="LinearLayout"><Expressions/><location startLine="453" startOffset="20" endLine="535" endOffset="34"/></Target><Target id="@+id/draftsCard" view="LinearLayout"><Expressions/><location startLine="460" startOffset="24" endLine="495" endOffset="38"/></Target><Target id="@+id/txtDraftsCount" view="TextView"><Expressions/><location startLine="469" startOffset="28" endLine="483" endOffset="67"/></Target><Target id="@+id/outstandingCard" view="LinearLayout"><Expressions/><location startLine="499" startOffset="24" endLine="534" endOffset="38"/></Target><Target id="@+id/txtOutstandingCount" view="TextView"><Expressions/><location startLine="508" startOffset="28" endLine="522" endOffset="67"/></Target><Target id="@+id/activeCard" view="LinearLayout"><Expressions/><location startLine="548" startOffset="24" endLine="583" endOffset="38"/></Target><Target id="@+id/txtActiveCount" view="TextView"><Expressions/><location startLine="557" startOffset="28" endLine="571" endOffset="67"/></Target><Target id="@+id/completedCard" view="LinearLayout"><Expressions/><location startLine="587" startOffset="24" endLine="622" endOffset="38"/></Target><Target id="@+id/txtCompletedCount" view="TextView"><Expressions/><location startLine="596" startOffset="28" endLine="610" endOffset="67"/></Target><Target id="@+id/addNewEstimation" view="ImageView"><Expressions/><location startLine="626" startOffset="20" endLine="634" endOffset="62"/></Target></Targets></Layout>