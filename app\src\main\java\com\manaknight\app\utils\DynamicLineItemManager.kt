package com.manaknight.app.utils

import Manaknight.R
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.ImageView
//import com.manaknight.app.R
import com.manaknight.app.model.remote.profitPro.JobDetailsRespModel
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel2
import androidx.navigation.NavController
import com.manaknight.app.ui.fragments.home.LineItemsFragmentDirections

class DynamicLineItemManager(
    private val context: Context,
    private val container: LinearLayout,
    private val navController: NavController,
    private val projectID: Int,
    private val onDeleteItem: (Int) -> Unit
) {

    companion object {
        private const val BASE_LINE_ITEM_ID = 1000
        private const val BASE_DESCRIPTION_ID = 2000
        private const val BASE_SALE_PRICE_ID = 3000
        private const val BASE_PROFIT_OVERHEAD_ID = 4000
        private const val BASE_LABOR_BUDGET_ID = 5000
        private const val BASE_MATERIAL_BUDGET_ID = 6000
        private const val BASE_TYPE_LABEL_ID = 7000
        private const val BASE_DELETE_BTN_ID = 8000
        private const val BASE_EDIT_BTN_ID = 9000
    }

    fun updateLineItems(lineItems: List<JobDetailsRespModel>) {
        // Clear existing views
        container.removeAllViews()
        
        // Add line items dynamically with proper numbering
        lineItems.forEachIndexed { index, lineItem ->
            val lineItemView = createLineItemView(lineItem, index + 1)
            container.addView(lineItemView)
        }
    }

    private fun createLineItemView(lineItem: JobDetailsRespModel, position: Int): View {
        val inflater = LayoutInflater.from(context)
        val lineItemView = inflater.inflate(R.layout.item_line, container, false)
        
        // Generate unique IDs for this line item
        val lineItemId = BASE_LINE_ITEM_ID + position
        val descriptionId = BASE_DESCRIPTION_ID + position
        val salePriceId = BASE_SALE_PRICE_ID + position
        val profitOverheadId = BASE_PROFIT_OVERHEAD_ID + position
        val laborBudgetId = BASE_LABOR_BUDGET_ID + position
        val materialBudgetId = BASE_MATERIAL_BUDGET_ID + position
        val typeLabelId = BASE_TYPE_LABEL_ID + position
        val deleteBtnId = BASE_DELETE_BTN_ID + position
        val editBtnId = BASE_EDIT_BTN_ID + position
        
        // Set IDs
        lineItemView.id = lineItemId
        
        // Find and configure views
        val description = lineItemView.findViewById<TextView>(R.id.description)
        val salePrice = lineItemView.findViewById<TextView>(R.id.txtSalePrice_1)
        val profitOverhead = lineItemView.findViewById<TextView>(R.id.txtProfitOverhead_1)
        val laborBudget = lineItemView.findViewById<TextView>(R.id.txtLaboutBudget_1)
        val materialBudget = lineItemView.findViewById<TextView>(R.id.txtMaterialBudget_1)
        val typeLabel = lineItemView.findViewById<TextView>(R.id.txtTypeLabel)
        val deleteBtn = lineItemView.findViewById<ImageView>(R.id.btnDelete)
        val editBtn = lineItemView.findViewById<ImageView>(R.id.btnEdit)
        
        // Set content with dynamic numbering
        description.text = "${position}. ${lineItem.description}"
        salePrice.text = "$${lineItem.sale_price?.toInt() ?: 0}"
        profitOverhead.text = "$${lineItem.profit_overhead_amount?.toInt() ?: 0}"
        laborBudget.text = "$${lineItem.labour_budget?.toInt() ?: 0}"
        materialBudget.text = "$${lineItem.material_budget?.toInt() ?: 0}"
        
        // Set type label based on estimated_by
        typeLabel.text = when (lineItem.estimated_by) {
            "material" -> "Material & Labor"
            "lineal_foot" -> "Linear Foot"
            "square_foot" -> "Square Foot"
            else -> "Unknown"
        }
        
        // Set up click listeners
        deleteBtn.setOnClickListener {
            lineItem.line_id?.let { id -> onDeleteItem(id) }
        }
        
        editBtn.setOnClickListener {
            navigateToEditScreen(lineItem)
        }
        
        return lineItemView
    }
    
    private fun navigateToEditScreen(lineItem: JobDetailsRespModel) {
        val action = when (lineItem.estimated_by) {
            "material" -> {
                LineItemsFragmentDirections.actionLineItemViewToAddMateriallineItemView(
                    MaterialRespListModel2(lineItem.materials),
                    projectID,
                    1,
                    lineItem.description ?: "",
                    "material",
                    1,
                    "${lineItem.labour_hours}",
                    lineItem.line_id ?: 0
                )
            }
            "lineal_foot", "square_foot" -> {
                LineItemsFragmentDirections.actionLineItemViewToAddLinearlineItemView(
                    MaterialRespListModel2(lineItem.materials),
                    projectID,
                    1,
                    lineItem.description ?: "",
                    lineItem.estimated_by,
                    1,
                    "30",
                    lineItem.line_id ?: 0
                )
            }
            else -> null
        }
        
        action?.let { navController.navigate(it) }
    }
} 